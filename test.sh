#!/bin/bash

# Swift-FFmpeg 测试脚本

echo "=== Swift-FFmpeg 测试脚本 ==="
echo

# 检查 Swift 是否可用
if ! command -v swift &> /dev/null; then
    echo "错误: Swift 未安装或不在 PATH 中"
    exit 1
fi

echo "✓ Swift 可用"

# 编译 swift-ffmpeg
echo "正在编译 swift-ffmpeg..."
if swiftc -O main.swift -o swift-ffmpeg; then
    echo "✓ 编译成功"
else
    echo "✗ 编译失败"
    exit 1
fi

# 测试帮助信息
echo
echo "=== 测试帮助信息 ==="
./swift-ffmpeg

echo
echo "=== 测试完成 ==="
echo "可执行文件已生成: ./swift-ffmpeg"
echo
echo "使用示例:"
echo "  ./swift-ffmpeg -i video.mp4 -f json -o metadata.json"
echo "  ./swift-ffmpeg -i video.mp4 -ss 10 -f png -o frame.png"
echo "  ./swift-ffmpeg -i input.mp4 -codec:v hevc -size 720p -o output.mp4"
