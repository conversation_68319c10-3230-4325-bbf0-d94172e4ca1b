#!/usr/bin/env python3
"""
关键帧检测结果分析脚本
对比不同方法检测到的关键帧结果
支持直接运行swift-ffmpeg命令获取数据
"""

import json
import sys
import subprocess
import os
import tempfile
import time
from typing import List, Dict, Any
from pathlib import Path

def load_json(filename: str) -> Dict[str, Any]:
    """加载JSON文件"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {filename}")
        return {}
    except json.JSONDecodeError:
        print(f"错误: {filename} 不是有效的JSON文件")
        return {}

def run_swift_ffmpeg(video_file: str, output_file: str, extra_args: List[str] = None) -> bool:
    """运行swift-ffmpeg命令获取关键帧数据"""
    try:
        cmd = ["swift", "main.swift", "-i", video_file, "-f", "json", "-o", output_file]
        if extra_args:
            cmd.extend(extra_args)

        print(f"运行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

        if result.returncode == 0:
            print(f"✅ 成功生成: {output_file}")
            return True
        else:
            print(f"❌ 命令执行失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ 命令执行超时")
        return False
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        return False

def cleanup_files(files: List[str]):
    """清理临时文件"""
    for file in files:
        try:
            if os.path.exists(file):
                os.remove(file)
                print(f"🗑️  已删除临时文件: {file}")
        except Exception as e:
            print(f"⚠️  删除文件失败 {file}: {e}")

def format_time(seconds: float) -> str:
    """格式化时间显示"""
    minutes = int(seconds // 60)
    secs = seconds % 60
    return f"{minutes:02d}:{secs:06.3f}"

def analyze_keyframes(keyframes1: List[float], keyframes2: List[float],
                     name1: str = "方法1", name2: str = "方法2") -> Dict[str, Any]:
    """分析两组关键帧的差异"""

    # 精确到0.01秒进行比较
    set1 = set(round(kf * 100) / 100 for kf in keyframes1)
    set2 = set(round(kf * 100) / 100 for kf in keyframes2)

    intersection = set1.intersection(set2)
    union = set1.union(set2)

    only_in_1 = set1 - set2
    only_in_2 = set2 - set1

    overlap_ratio = len(intersection) / len(union) if union else 0

    # 计算平均间隔
    def calc_avg_interval(kfs):
        if len(kfs) < 2:
            return 0
        intervals = [kfs[i+1] - kfs[i] for i in range(len(kfs)-1)]
        return sum(intervals) / len(intervals)

    avg_interval_1 = calc_avg_interval(sorted(keyframes1))
    avg_interval_2 = calc_avg_interval(sorted(keyframes2))

    return {
        'count_1': len(keyframes1),
        'count_2': len(keyframes2),
        'overlap_count': len(intersection),
        'overlap_ratio': overlap_ratio,
        'only_in_1': len(only_in_1),
        'only_in_2': len(only_in_2),
        'avg_interval_1': avg_interval_1,
        'avg_interval_2': avg_interval_2,
        'only_in_1_times': sorted(only_in_1),
        'only_in_2_times': sorted(only_in_2)
    }

def analyze_video_keyframes(video_file: str, auto_cleanup: bool = True) -> bool:
    """分析视频文件的关键帧检测结果"""

    if not os.path.exists(video_file):
        print(f"❌ 视频文件不存在: {video_file}")
        return False

    video_name = Path(video_file).stem
    print(f"=== {video_file} 关键帧检测结果分析 ===\n")

    # 生成临时文件名
    temp_files = []
    comparison_file = f"{video_name}_comparison.json"
    current_file = f"{video_name}_current.json"
    full_file = f"{video_name}_full.json"

    temp_files.extend([comparison_file, current_file, full_file])

    try:
        # 1. 运行对比模式
        print("🔄 正在运行关键帧对比检测...")
        if not run_swift_ffmpeg(video_file, comparison_file, ["-compare-keyframes"]):
            return False

        # 2. 运行当前默认模式
        print("🔄 正在运行默认模式检测...")
        if not run_swift_ffmpeg(video_file, current_file):
            return False

        # 3. 运行完整模式
        print("🔄 正在运行完整模式检测...")
        if not run_swift_ffmpeg(video_file, full_file, ["-full-keyframes"]):
            return False

        print("\n" + "="*50)
        print("📊 开始分析检测结果...")
        print("="*50 + "\n")

        # 加载所有结果
        comparison_data = load_json(comparison_file)
        current_data = load_json(current_file)
        full_data = load_json(full_file)

        if not all([comparison_data, current_data, full_data]):
            print("❌ 无法加载所有检测结果")
            return False

        # 执行分析
        return perform_analysis(comparison_data, current_data, full_data, video_file)

    finally:
        # 清理临时文件
        if auto_cleanup:
            print("\n" + "="*50)
            print("🧹 清理临时文件...")
            cleanup_files(temp_files)

def main():
    """主函数 - 支持命令行参数"""
    if len(sys.argv) < 2:
        # 默认行为：尝试分析rou.mp4
        video_file = "rou.mp4"
        if not os.path.exists(video_file):
            print("用法: python3 analyze_keyframes.py <视频文件> [--keep-files]")
            print("或者确保当前目录有 rou.mp4 文件")
            return
    else:
        video_file = sys.argv[1]

    # 检查是否保留临时文件
    keep_files = "--keep-files" in sys.argv
    auto_cleanup = not keep_files

    if keep_files:
        print("📝 注意: 将保留临时文件用于调试")

    success = analyze_video_keyframes(video_file, auto_cleanup)
    if success:
        print("\n✅ 分析完成!")
    else:
        print("\n❌ 分析失败!")

def perform_analysis(comparison_data: Dict[str, Any], current_data: Dict[str, Any],
                    full_data: Dict[str, Any], video_file: str) -> bool:
    """执行详细的关键帧分析"""

    print(f"视频信息:")
    print(f"  文件: {video_file}")
    print(f"  时长: {format_time(comparison_data.get('video_duration', 0))}")
    print(f"  分辨率: {current_data.get('width', 0)}x{current_data.get('height', 0)}")
    print(f"  帧率: {current_data.get('fps', 0):.2f} fps")
    print(f"  码率: {current_data.get('bitrate', 0):,} bps")
    print()

    # 从对比数据中提取关键帧
    smart_keyframes = comparison_data['smart_mode']['keyframes']
    full_keyframes = comparison_data['full_mode']['keyframes']
    current_keyframes = current_data.get('keyframes', [])
    full_mode_keyframes = full_data.get('keyframes', []) if full_data else []

    print("=== 检测性能对比 ===")
    smart_time = comparison_data['smart_mode']['detection_time']
    full_time = comparison_data['full_mode']['detection_time']

    print(f"智能快速模式: {smart_time:.3f}秒, {len(smart_keyframes)}个关键帧")
    print(f"完整模式:     {full_time:.3f}秒, {len(full_keyframes)}个关键帧")
    print(f"当前默认模式: 未测量, {len(current_keyframes)}个关键帧")
    print(f"速度提升:     {full_time/smart_time:.1f}x")
    print()

    # 对比智能模式 vs 完整模式
    print("=== 智能模式 vs 完整模式 ===")
    analysis1 = analyze_keyframes(smart_keyframes, full_keyframes, "智能模式", "完整模式")
    print(f"重叠度: {analysis1['overlap_ratio']*100:.1f}%")
    print(f"仅智能模式: {analysis1['only_in_1']}个")
    print(f"仅完整模式: {analysis1['only_in_2']}个")
    print(f"平均间隔: 智能{analysis1['avg_interval_1']:.2f}s, 完整{analysis1['avg_interval_2']:.2f}s")
    print()

    # 对比当前默认模式 vs 智能模式
    print("=== 当前默认模式 vs 智能模式 ===")
    analysis2 = analyze_keyframes(current_keyframes, smart_keyframes, "当前默认", "智能模式")
    print(f"重叠度: {analysis2['overlap_ratio']*100:.1f}%")
    print(f"仅当前默认: {analysis2['only_in_1']}个")
    print(f"仅智能模式: {analysis2['only_in_2']}个")
    print(f"平均间隔: 当前{analysis2['avg_interval_1']:.2f}s, 智能{analysis2['avg_interval_2']:.2f}s")
    print()

    # 验证完整模式单独运行的结果
    if full_mode_keyframes:
        print("=== 完整模式单独运行验证 ===")
        analysis_verify = analyze_keyframes(full_keyframes, full_mode_keyframes, "对比中完整模式", "单独运行完整模式")
        print(f"重叠度: {analysis_verify['overlap_ratio']*100:.1f}%")
        if analysis_verify['overlap_ratio'] == 1.0:
            print("✅ 完整模式结果一致")
        else:
            print("⚠️  完整模式结果不一致")
        print()

    # 可选：如果有历史测试数据，也进行对比（注意这是不同视频）
    old_data = load_json('test_keyframes.json')
    if old_data and 'keyframes' in old_data:
        old_keyframes = old_data['keyframes']
        print("=== 与历史测试结果对比（不同视频文件）===")
        print(f"历史测试视频: {old_data.get('duration', 0):.1f}秒, {old_data.get('width', 0)}x{old_data.get('height', 0)}")
        print(f"历史测试结果: {len(old_keyframes)}个关键帧")
        print(f"当前视频: {comparison_data.get('video_duration', 0):.1f}秒, {current_data.get('width', 0)}x{current_data.get('height', 0)}")
        print(f"当前结果: {len(current_keyframes)}个关键帧")
        print("注意: 这是两个完全不同的视频文件，对比仅供参考")
        print()

    # 显示关键帧时间点
    print("=== 关键帧时间点详情 ===")
    print("智能模式:")
    for i, kf in enumerate(smart_keyframes):
        print(f"  {i+1:2d}: {format_time(kf)}")

    print("\n完整模式:")
    for i, kf in enumerate(full_keyframes):
        print(f"  {i+1:2d}: {format_time(kf)}")

    print("\n当前默认模式:")
    for i, kf in enumerate(current_keyframes):
        print(f"  {i+1:2d}: {format_time(kf)}")

    # 结论
    print("\n=== 结论 ===")
    print(f"📹 视频特征: {current_data.get('width', 0)}x{current_data.get('height', 0)} @ {current_data.get('fps', 0):.1f}fps, 时长{comparison_data.get('video_duration', 0):.1f}秒")
    print()

    if analysis1['overlap_ratio'] >= 0.95:
        print("✅ 智能模式与完整模式结果高度一致")
    elif analysis1['overlap_ratio'] >= 0.8:
        print("⚠️  智能模式与完整模式结果基本一致，有少量差异")
    else:
        print("❌ 智能模式与完整模式结果差异较大")

    if smart_time < full_time:
        print(f"✅ 智能模式速度更快 ({full_time/smart_time:.1f}x)")
    else:
        print("⚠️  智能模式速度优势不明显")

    if analysis2['overlap_ratio'] >= 0.95:
        print("✅ 当前默认模式与智能模式结果高度一致")
    else:
        print("⚠️  当前默认模式与智能模式存在差异")

    # 关键帧密度分析
    duration = comparison_data.get('video_duration', 0)
    if duration > 0:
        keyframe_density = len(current_keyframes) / duration
        print(f"📊 关键帧密度: {keyframe_density:.2f} 个/秒")

        if keyframe_density < 0.5:
            print("   → 关键帧较稀疏，适合长视频或静态内容")
        elif keyframe_density < 2:
            print("   → 关键帧密度适中，适合一般视频内容")
        else:
            print("   → 关键帧较密集，可能是高动态内容或短视频")

    print(f"\n🎯 对于此视频，两种检测方法表现一致，建议使用智能快速模式以获得更好的性能。")

if __name__ == "__main__":
    main()
