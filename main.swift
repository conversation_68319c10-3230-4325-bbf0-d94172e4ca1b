#!/usr/bin/env swift

import Foundation
import AVFoundation
import <PERSON>Toolbox
import CoreMedia
import CoreVideo
import ImageIO
import UniformTypeIdentifiers

// MARK: - Command Line Arguments Structure
struct Arguments {
    var startTime: Double?
    var inputSource: String?
    var duration: Double?
    var format: String?
    var outputDestination: String?
    var preset: String?
    var maxFPS: Double?
    var videoCodec: String?
    var audioCodec: String?
    var keepHDR: Bool = false
    var outputTimestampOffset: Double?
    var size: String?
    var fullKeyframes: Bool = false

    static func parse() -> Arguments? {
        let args = CommandLine.arguments
        var arguments = Arguments()

        var i = 1
        while i < args.count {
            let arg = args[i]

            switch arg {
            case "-ss":
                guard i + 1 < args.count else { return nil }
                arguments.startTime = parseTimeString(args[i + 1])
                i += 2
            case "-i":
                guard i + 1 < args.count else { return nil }
                arguments.inputSource = args[i + 1]
                i += 2
            case "-t":
                guard i + 1 < args.count else { return nil }
                arguments.duration = parseTimeString(args[i + 1])
                i += 2
            case "-f":
                guard i + 1 < args.count else { return nil }
                arguments.format = args[i + 1]
                i += 2
            case "-o":
                guard i + 1 < args.count else { return nil }
                arguments.outputDestination = args[i + 1]
                i += 2
            case "-preset":
                guard i + 1 < args.count else { return nil }
                arguments.preset = args[i + 1]
                i += 2
            case "-r", "-max_fps":
                guard i + 1 < args.count else { return nil }
                arguments.maxFPS = Double(args[i + 1])
                i += 2
            case "-codec:v", "-vcodec":
                guard i + 1 < args.count else { return nil }
                arguments.videoCodec = args[i + 1]
                i += 2
            case "-codec:a", "-acodec":
                guard i + 1 < args.count else { return nil }
                arguments.audioCodec = args[i + 1]
                i += 2
            case "-keepHDR":
                arguments.keepHDR = true
                i += 1
            case "-output_ts_offset":
                guard i + 1 < args.count else { return nil }
                arguments.outputTimestampOffset = Double(args[i + 1])
                i += 2
            case "-size":
                guard i + 1 < args.count else { return nil }
                arguments.size = args[i + 1]
                i += 2
            case "-full-keyframes":
                arguments.fullKeyframes = true
                i += 1
            default:
                print("未知参数: \(arg)")
                return nil
            }
        }

        return arguments
    }

    static func parseTimeString(_ timeString: String) -> Double? {
        // 支持两种格式: 123.45 或 HH:MM:SS.ms
        if timeString.contains(":") {
            let components = timeString.split(separator: ":")
            guard components.count == 3 else { return nil }

            guard let hours = Double(components[0]),
                  let minutes = Double(components[1]),
                  let seconds = Double(components[2]) else { return nil }

            return hours * 3600 + minutes * 60 + seconds
        } else {
            return Double(timeString)
        }
    }
}

// MARK: - Resolution Helper
struct ResolutionHelper {
    static func parseResolutionTag(_ tag: String) -> Int? {
        switch tag.lowercased() {
        case "480p": return 480
        case "720p": return 720
        case "1080p": return 1080
        case "1440p": return 1440
        case "2160p": return 2160
        case "4320p": return 4320
        default: return Int(tag) // 支持直接输入数字
        }
    }

    static func calculateOutputSize(originalWidth: Int, originalHeight: Int, targetShortSide: Int) -> (width: Int, height: Int) {
        let isLandscape = originalWidth > originalHeight
        let shortSide = isLandscape ? originalHeight : originalWidth
        let longSide = isLandscape ? originalWidth : originalHeight

        // 如果原始短边已经小于或等于目标，保持原始分辨率
        if shortSide <= targetShortSide {
            return (originalWidth, originalHeight)
        }

        let ratio = Double(targetShortSide) / Double(shortSide)
        let newLongSide = Int(Double(longSide) * ratio)

        if isLandscape {
            return (newLongSide, targetShortSide)
        } else {
            return (targetShortSide, newLongSide)
        }
    }
}

// MARK: - Preset Mapping
struct PresetMapper {
    static func mapToAVPreset(_ preset: String) -> String {
        switch preset.lowercased() {
        case "ultrafast":
            return AVAssetExportPresetLowQuality
        case "fast":
            return AVAssetExportPresetMediumQuality
        case "medium":
            return AVAssetExportPresetHighestQuality
        case "slow":
            return AVAssetExportPresetHighestQuality
        case "veryslow":
            return AVAssetExportPresetHighestQuality
        default:
            return AVAssetExportPresetMediumQuality
        }
    }
}

// MARK: - Media Info Extractor
class MediaInfoExtractor {
    static func extractMetadata(from asset: AVAsset, fullKeyframes: Bool = false) async -> [String: Any] {
        var metadata: [String: Any] = [:]

        // 基本信息
        let duration = try? await asset.load(.duration)
        metadata["duration"] = duration?.seconds ?? 0

        // 视频轨道信息
        if let videoTrack = try? await asset.loadTracks(withMediaType: .video).first {
            let naturalSize = try? await videoTrack.load(.naturalSize)
            let nominalFrameRate = try? await videoTrack.load(.nominalFrameRate)
            let estimatedDataRate = try? await videoTrack.load(.estimatedDataRate)

            metadata["width"] = Int(naturalSize?.width ?? 0)
            metadata["height"] = Int(naturalSize?.height ?? 0)
            metadata["fps"] = nominalFrameRate ?? 0
            metadata["bitrate"] = Int(estimatedDataRate ?? 0)

            // 视频编码信息
            if let formatDescriptions = try? await videoTrack.load(.formatDescriptions),
               let formatDescription = formatDescriptions.first {
                let codecType = CMFormatDescriptionGetMediaSubType(formatDescription)
                metadata["video_codec"] = fourCCToString(codecType)
            }
        }

        // 音频轨道信息
        if let audioTrack = try? await asset.loadTracks(withMediaType: .audio).first {
            if let formatDescriptions = try? await audioTrack.load(.formatDescriptions),
               let formatDescription = formatDescriptions.first {
                let codecType = CMFormatDescriptionGetMediaSubType(formatDescription)
                metadata["audio_codec"] = fourCCToString(codecType)
            }
        }

        // 关键帧列表
        metadata["keyframes"] = await extractKeyframes(from: asset, fastMode: !fullKeyframes)

        // 用户自定义元数据
        let commonMetadata = try? await asset.load(.commonMetadata)
        var userMetadata: [String: String] = [:]
        for item in commonMetadata ?? [] {
            if let key = item.commonKey?.rawValue,
               let value = try? await item.load(.stringValue) {
                userMetadata[key] = value
            }
        }
        metadata["metadata"] = userMetadata

        return metadata
    }

    static func extractKeyframes(from asset: AVAsset, fastMode: Bool = true) async -> [Double] {
        guard let videoTrack = try? await asset.loadTracks(withMediaType: .video).first else {
            print("警告: 无法找到视频轨道，无法提取关键帧")
            return []
        }

        if fastMode {
            // 智能快速模式：使用 AVAssetImageGenerator 精确定位
            return await extractKeyframesSmart(from: asset, videoTrack: videoTrack)
        } else {
            // 完整模式：检测所有关键帧（慢但最准确）
            return await extractKeyframesFull(from: asset, videoTrack: videoTrack)
        }
    }

    private static func extractKeyframesSmart(from asset: AVAsset, videoTrack: AVAssetTrack) async -> [Double] {
        // 智能快速模式：使用有限的样本检测真实关键帧
        var keyframes: [Double] = []

        do {
            // 创建 AVAssetReader，但只读取有限的样本
            let reader = try AVAssetReader(asset: asset)
            let videoOutput = AVAssetReaderTrackOutput(track: videoTrack, outputSettings: nil)

            guard reader.canAdd(videoOutput) else {
                print("警告: 无法添加视频输出到读取器")
                return [0.0]
            }

            reader.add(videoOutput)

            guard reader.startReading() else {
                print("警告: 无法开始读取视频")
                return [0.0]
            }

            var sampleCount = 0
            let maxSamples = 2000 // 只检查前2000个样本，大约对应前几分钟
            var lastKeyframeTime: Double = -1

            // 读取有限样本并检测关键帧
            while reader.status == .reading && sampleCount < maxSamples {
                if let sampleBuffer = videoOutput.copyNextSampleBuffer() {
                    sampleCount += 1

                    // 检查是否为关键帧
                    let attachments = CMSampleBufferGetSampleAttachmentsArray(sampleBuffer, createIfNecessary: false)

                    if let attachmentsArray = attachments as? [[CFString: Any]] {
                        for attachment in attachmentsArray {
                            let notSync = attachment[kCMSampleAttachmentKey_NotSync] as? Bool ?? false

                            if !notSync {
                                let presentationTime = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)
                                let timeInSeconds = presentationTime.seconds

                                if timeInSeconds.isFinite && timeInSeconds >= 0 {
                                    keyframes.append(timeInSeconds)
                                    lastKeyframeTime = timeInSeconds
                                }
                            }
                        }
                    } else if keyframes.isEmpty {
                        // 如果没有附件信息，假设第一帧是关键帧
                        let presentationTime = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)
                        let timeInSeconds = presentationTime.seconds
                        if timeInSeconds.isFinite && timeInSeconds >= 0 {
                            keyframes.append(timeInSeconds)
                        }
                    }
                } else {
                    break
                }
            }

            reader.cancelReading()

            // 基于检测到的关键帧估算后续的关键帧
            if keyframes.count >= 2 {
                let intervals = zip(keyframes.dropFirst(), keyframes).map { $0.0 - $0.1 }
                let avgInterval = intervals.reduce(0, +) / Double(intervals.count)

                // 使用平均间隔估算剩余的关键帧
                let duration = (try? await asset.load(.duration))?.seconds ?? 0
                var estimatedTime = lastKeyframeTime + avgInterval

                while estimatedTime < duration {
                    keyframes.append(estimatedTime)
                    estimatedTime += avgInterval
                }
            }

        } catch {
            print("警告: 智能关键帧提取失败: \(error.localizedDescription)")
            keyframes = [0.0]
        }

        return Array(Set(keyframes)).sorted()
    }

    private static func extractKeyframesFast(from asset: AVAsset, videoTrack: AVAssetTrack) async -> [Double] {
        // 智能快速模式：使用 AVAssetImageGenerator 的精确定位能力
        let duration = (try? await asset.load(.duration))?.seconds ?? 0
        var keyframes: [Double] = []

        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.requestedTimeToleranceAfter = .zero
        imageGenerator.requestedTimeToleranceBefore = .zero

        // 策略：使用二分搜索和采样来快速找到关键帧
        // 1. 先检测几个固定点来了解关键帧分布
        let samplePoints = [0.0, duration * 0.1, duration * 0.3, duration * 0.5, duration * 0.7, duration * 0.9]

        for sampleTime in samplePoints {
            if sampleTime < duration {
                let time = CMTime(seconds: sampleTime, preferredTimescale: 600)
                do {
                    let result = try await imageGenerator.image(at: time)
                    let actualTime = result.actualTime.seconds
                    if actualTime.isFinite && actualTime >= 0 {
                        keyframes.append(actualTime)
                    }
                } catch {
                    // 忽略错误，继续下一个采样点
                }
            }
        }

        // 2. 基于采样结果，在间隔较大的区域进行补充采样
        keyframes = Array(Set(keyframes)).sorted()

        var finalKeyframes = keyframes

        // 在间隔超过10秒的区域进行补充采样
        for i in 0..<keyframes.count - 1 {
            let gap = keyframes[i + 1] - keyframes[i]
            if gap > 10.0 {
                // 在这个间隔中间再采样几个点
                let midPoint = keyframes[i] + gap / 2
                let time = CMTime(seconds: midPoint, preferredTimescale: 600)
                do {
                    let result = try await imageGenerator.image(at: time)
                    let actualTime = result.actualTime.seconds
                    if actualTime.isFinite && actualTime >= 0 {
                        finalKeyframes.append(actualTime)
                    }
                } catch {
                    // 忽略错误
                }
            }
        }

        return Array(Set(finalKeyframes)).sorted()
    }

    private static func extractKeyframesFull(from asset: AVAsset, videoTrack: AVAssetTrack) async -> [Double] {
        var keyframes: [Double] = []

        do {
            // 创建 AVAssetReader 来读取视频样本
            let reader = try AVAssetReader(asset: asset)

            // 配置视频输出，只读取关键帧信息
            let videoOutput = AVAssetReaderTrackOutput(track: videoTrack, outputSettings: nil)

            guard reader.canAdd(videoOutput) else {
                print("警告: 无法添加视频输出到读取器")
                return [0.0]
            }

            reader.add(videoOutput)

            guard reader.startReading() else {
                print("警告: 无法开始读取视频")
                return [0.0]
            }

            var sampleCount = 0
            let maxSamples = 10000 // 大幅减少样本数，提高速度

            // 读取样本并检测关键帧
            while reader.status == .reading && sampleCount < maxSamples {
                if let sampleBuffer = videoOutput.copyNextSampleBuffer() {
                    sampleCount += 1

                    // 检查是否为关键帧（同步样本）
                    let attachments = CMSampleBufferGetSampleAttachmentsArray(sampleBuffer, createIfNecessary: false)

                    if let attachmentsArray = attachments as? [[CFString: Any]] {
                        for attachment in attachmentsArray {
                            // 检查 kCMSampleAttachmentKey_NotSync 键
                            let notSync = attachment[kCMSampleAttachmentKey_NotSync] as? Bool ?? false

                            if !notSync {
                                // 这是一个关键帧，获取其时间戳
                                let presentationTime = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)
                                let timeInSeconds = presentationTime.seconds

                                if timeInSeconds.isFinite && timeInSeconds >= 0 {
                                    keyframes.append(timeInSeconds)
                                }
                            }
                        }
                    } else {
                        // 如果没有附件信息，假设第一帧是关键帧
                        if keyframes.isEmpty {
                            let presentationTime = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)
                            let timeInSeconds = presentationTime.seconds
                            if timeInSeconds.isFinite && timeInSeconds >= 0 {
                                keyframes.append(timeInSeconds)
                            }
                        }
                    }
                } else {
                    break
                }
            }

            reader.cancelReading()

            if sampleCount >= maxSamples {
                print("警告: 样本数量过多，已限制检测范围")
            }

        } catch {
            print("警告: 完整关键帧提取失败: \(error.localizedDescription)")
            keyframes = [0.0]
        }

        // 确保关键帧列表是排序的且去重的
        keyframes = Array(Set(keyframes)).sorted()

        // 如果没有检测到关键帧，至少添加第一帧
        if keyframes.isEmpty {
            keyframes.append(0.0)
        }

        return keyframes
    }

    static func fourCCToString(_ fourCC: FourCharCode) -> String {
        let bytes = [
            UInt8((fourCC >> 24) & 0xFF),
            UInt8((fourCC >> 16) & 0xFF),
            UInt8((fourCC >> 8) & 0xFF),
            UInt8(fourCC & 0xFF)
        ]
        return String(bytes: bytes, encoding: .ascii) ?? "unknown"
    }
}

// MARK: - Frame Extractor
class FrameExtractor {
    static func extractFrame(from asset: AVAsset, at time: CMTime, outputSize: CGSize? = nil) async throws -> CGImage? {
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.requestedTimeToleranceAfter = .zero
        imageGenerator.requestedTimeToleranceBefore = .zero

        if let outputSize = outputSize {
            imageGenerator.maximumSize = outputSize
        }

        do {
            let cgImage = try await imageGenerator.image(at: time).image
            return cgImage
        } catch {
            print("提取帧失败: \(error.localizedDescription)")
            throw error
        }
    }

    static func saveImageAsPNG(_ image: CGImage, to url: URL) throws {
        guard let destination = CGImageDestinationCreateWithURL(url as CFURL, UTType.png.identifier as CFString, 1, nil) else {
            throw NSError(domain: "FrameExtractor", code: 1, userInfo: [NSLocalizedDescriptionKey: "无法创建图像目标"])
        }

        CGImageDestinationAddImage(destination, image, nil)

        if !CGImageDestinationFinalize(destination) {
            throw NSError(domain: "FrameExtractor", code: 2, userInfo: [NSLocalizedDescriptionKey: "保存PNG失败"])
        }
    }
}

// MARK: - Video Transcoder
class VideoTranscoder {
    private let arguments: Arguments

    init(arguments: Arguments) {
        self.arguments = arguments
    }

    func process() async throws {
        guard let inputSource = arguments.inputSource else {
            throw NSError(domain: "VideoTranscoder", code: 1, userInfo: [NSLocalizedDescriptionKey: "缺少输入源"])
        }

        print("正在加载输入源: \(inputSource)")

        let asset: AVAsset
        if inputSource.hasPrefix("http://") || inputSource.hasPrefix("https://") {
            asset = AVURLAsset(url: URL(string: inputSource)!)
        } else {
            asset = AVURLAsset(url: URL(fileURLWithPath: inputSource))
        }

        // 等待资产加载
        let isPlayable = try await asset.load(.isPlayable)
        guard isPlayable else {
            throw NSError(domain: "VideoTranscoder", code: 2, userInfo: [NSLocalizedDescriptionKey: "输入文件不可播放"])
        }

        print("输入源加载成功")

        // 根据格式执行不同的处理
        switch arguments.format?.lowercased() {
        case "json":
            try await processMetadataExtraction(asset: asset)
        case "png":
            try await processFrameExtraction(asset: asset)
        default:
            try await processVideoTranscoding(asset: asset)
        }
    }

    private func processMetadataExtraction(asset: AVAsset) async throws {
        print("正在提取元数据...")
        let metadata = await MediaInfoExtractor.extractMetadata(from: asset, fullKeyframes: arguments.fullKeyframes)

        let jsonData = try JSONSerialization.data(withJSONObject: metadata, options: .prettyPrinted)

        if let outputDestination = arguments.outputDestination {
            if outputDestination == "pipe:1" {
                print(String(data: jsonData, encoding: .utf8) ?? "")
            } else {
                try jsonData.write(to: URL(fileURLWithPath: outputDestination))
                print("元数据已保存到: \(outputDestination)")
            }
        } else {
            print(String(data: jsonData, encoding: .utf8) ?? "")
        }
    }

    private func processFrameExtraction(asset: AVAsset) async throws {
        print("正在提取帧...")

        let startTime = arguments.startTime ?? 0
        let time = CMTime(seconds: startTime, preferredTimescale: 600)

        var outputSize: CGSize?
        if let sizeTag = arguments.size,
           let targetShortSide = ResolutionHelper.parseResolutionTag(sizeTag) {

            // 获取原始视频尺寸
            if let videoTrack = try await asset.loadTracks(withMediaType: .video).first {
                let naturalSize = try await videoTrack.load(.naturalSize)
                let originalWidth = Int(naturalSize.width)
                let originalHeight = Int(naturalSize.height)

                let newSize = ResolutionHelper.calculateOutputSize(
                    originalWidth: originalWidth,
                    originalHeight: originalHeight,
                    targetShortSide: targetShortSide
                )
                outputSize = CGSize(width: newSize.width, height: newSize.height)
                print("输出尺寸: \(newSize.width)x\(newSize.height)")
            }
        }

        let image = try await FrameExtractor.extractFrame(from: asset, at: time, outputSize: outputSize)

        guard let cgImage = image else {
            throw NSError(domain: "VideoTranscoder", code: 3, userInfo: [NSLocalizedDescriptionKey: "提取帧失败"])
        }

        if let outputDestination = arguments.outputDestination {
            if outputDestination == "pipe:1" {
                // 对于管道输出，我们需要将图像数据写入stdout
                // 这里简化处理，实际应用中可能需要更复杂的实现
                print("PNG数据输出到管道暂不支持")
            } else {
                let outputURL = URL(fileURLWithPath: outputDestination)
                try FrameExtractor.saveImageAsPNG(cgImage, to: outputURL)
                print("帧已保存到: \(outputDestination)")
            }
        } else {
            throw NSError(domain: "VideoTranscoder", code: 4, userInfo: [NSLocalizedDescriptionKey: "缺少输出目标"])
        }
    }

    private func processVideoTranscoding(asset: AVAsset) async throws {
        print("正在开始视频转码...")

        guard let outputDestination = arguments.outputDestination else {
            throw NSError(domain: "VideoTranscoder", code: 5, userInfo: [NSLocalizedDescriptionKey: "缺少输出目标"])
        }

        // 创建时间范围
        let startTime = arguments.startTime ?? 0
        let assetDuration = (try? await asset.load(.duration))?.seconds ?? 0
        let duration = arguments.duration ?? (assetDuration - startTime)

        let timeRange = CMTimeRange(
            start: CMTime(seconds: startTime, preferredTimescale: 600),
            duration: CMTime(seconds: duration, preferredTimescale: 600)
        )

        print("处理时间范围: \(startTime)s - \(startTime + duration)s")

        if outputDestination == "pipe:1" {
            try await transcodeToStdout(asset: asset, timeRange: timeRange)
        } else {
            try await transcodeToFile(asset: asset, timeRange: timeRange, outputPath: outputDestination)
        }
    }

    private func transcodeToFile(asset: AVAsset, timeRange: CMTimeRange, outputPath: String) async throws {
        let outputURL = URL(fileURLWithPath: outputPath)

        // 删除已存在的输出文件
        if FileManager.default.fileExists(atPath: outputPath) {
            try FileManager.default.removeItem(atPath: outputPath)
        }

        // 确定输出格式
        let outputFormat = arguments.format?.lowercased() ?? "mp4"
        let fileType: AVFileType

        switch outputFormat {
        case "mp4":
            fileType = .mp4
        case "mov":
            fileType = .mov
        case "m4s":
            fileType = .mp4 // fMP4 使用 mp4 容器
        default:
            fileType = .mp4
        }

        // 检查是否需要自定义分辨率或其他高级设置
        let needsCustomSettings = arguments.size != nil || arguments.maxFPS != nil || arguments.videoCodec != nil

        if needsCustomSettings {
            // 使用 AVAssetReader/Writer 进行高级转码
            try await transcodeWithCustomSettings(asset: asset, timeRange: timeRange, outputURL: outputURL, fileType: fileType)
        } else {
            // 使用 AVAssetExportSession 进行简单转码，但使用保持原始质量的预设
            try await transcodeWithExportSession(asset: asset, timeRange: timeRange, outputURL: outputURL, fileType: fileType)
        }
    }

    private func transcodeWithExportSession(asset: AVAsset, timeRange: CMTimeRange, outputURL: URL, fileType: AVFileType) async throws {
        // 使用保持原始质量的预设
        let preset = arguments.preset != nil ? PresetMapper.mapToAVPreset(arguments.preset!) : AVAssetExportPresetPassthrough

        guard let exportSession = AVAssetExportSession(asset: asset, presetName: preset) else {
            throw NSError(domain: "VideoTranscoder", code: 6, userInfo: [NSLocalizedDescriptionKey: "无法创建导出会话"])
        }

        exportSession.outputURL = outputURL
        exportSession.outputFileType = fileType
        exportSession.timeRange = timeRange

        // 应用时间戳偏移
        if let offset = arguments.outputTimestampOffset {
            exportSession.timeRange = CMTimeRange(
                start: CMTimeAdd(timeRange.start, CMTime(seconds: offset, preferredTimescale: 600)),
                duration: timeRange.duration
            )
        }

        print("开始导出...")

        do {
            try await exportSession.export(to: outputURL, as: fileType)
            print("转码完成: \(outputURL.path)")
        } catch {
            throw NSError(domain: "VideoTranscoder", code: 7, userInfo: [NSLocalizedDescriptionKey: "导出失败: \(error.localizedDescription)"])
        }
    }

    private func transcodeWithCustomSettings(asset: AVAsset, timeRange: CMTimeRange, outputURL: URL, fileType: AVFileType) async throws {
        // 对于自定义设置，我们使用 AVAssetExportSession 配合 AVVideoComposition
        // 这样可以处理分辨率缩放等高级功能

        guard let videoTrack = try await asset.loadTracks(withMediaType: .video).first else {
            throw NSError(domain: "VideoTranscoder", code: 11, userInfo: [NSLocalizedDescriptionKey: "找不到视频轨道"])
        }

        // 获取原始视频信息
        let naturalSize = try await videoTrack.load(.naturalSize)
        let originalWidth = Int(naturalSize.width)
        let originalHeight = Int(naturalSize.height)

        // 计算输出尺寸
        var outputWidth = originalWidth
        var outputHeight = originalHeight

        if let sizeTag = arguments.size,
           let targetShortSide = ResolutionHelper.parseResolutionTag(sizeTag) {
            let newSize = ResolutionHelper.calculateOutputSize(
                originalWidth: originalWidth,
                originalHeight: originalHeight,
                targetShortSide: targetShortSide
            )
            outputWidth = newSize.width
            outputHeight = newSize.height
            print("输出尺寸: \(outputWidth)x\(outputHeight)")
        } else {
            print("保持原始尺寸: \(outputWidth)x\(outputHeight)")
        }

        // 创建视频合成
        let videoComposition = AVMutableVideoComposition()
        videoComposition.frameDuration = CMTime(value: 1, timescale: 30)
        videoComposition.renderSize = CGSize(width: outputWidth, height: outputHeight)

        let instruction = AVMutableVideoCompositionInstruction()
        instruction.timeRange = timeRange

        let layerInstruction = AVMutableVideoCompositionLayerInstruction(assetTrack: videoTrack)

        // 计算缩放变换（直接缩放，不保持宽高比）
        let scaleX = CGFloat(outputWidth) / naturalSize.width
        let scaleY = CGFloat(outputHeight) / naturalSize.height
        let transform = CGAffineTransform(scaleX: scaleX, y: scaleY)

        layerInstruction.setTransform(transform, at: .zero)

        instruction.layerInstructions = [layerInstruction]
        videoComposition.instructions = [instruction]

        // 使用高质量预设
        let preset = AVAssetExportPresetHighestQuality
        guard let exportSession = AVAssetExportSession(asset: asset, presetName: preset) else {
            throw NSError(domain: "VideoTranscoder", code: 6, userInfo: [NSLocalizedDescriptionKey: "无法创建导出会话"])
        }

        exportSession.outputURL = outputURL
        exportSession.outputFileType = fileType
        exportSession.timeRange = timeRange
        exportSession.videoComposition = videoComposition

        // 应用时间戳偏移
        if let offset = arguments.outputTimestampOffset {
            exportSession.timeRange = CMTimeRange(
                start: CMTimeAdd(timeRange.start, CMTime(seconds: offset, preferredTimescale: 600)),
                duration: timeRange.duration
            )
        }

        print("开始自定义转码...")

        do {
            try await exportSession.export(to: outputURL, as: fileType)
            print("转码完成: \(outputURL.path)")
        } catch {
            throw NSError(domain: "VideoTranscoder", code: 7, userInfo: [NSLocalizedDescriptionKey: "导出失败: \(error.localizedDescription)"])
        }
    }

    private func getVideoCodec() -> String {
        guard let codec = arguments.videoCodec?.lowercased() else {
            return AVVideoCodecType.h264.rawValue
        }

        switch codec {
        case "h264":
            return AVVideoCodecType.h264.rawValue
        case "hevc", "h265":
            return AVVideoCodecType.hevc.rawValue
        case "av1":
            // AV1 编码支持需要更新的 macOS 版本
            return AVVideoCodecType.hevc.rawValue
        default:
            return AVVideoCodecType.h264.rawValue
        }
    }

    private func transcodeToStdout(asset: AVAsset, timeRange: CMTimeRange) async throws {
        // 对于管道输出，我们需要使用 AVAssetReader 和 AVAssetWriter
        // 这是一个更复杂的实现，这里提供简化版本
        print("管道输出功能需要更复杂的实现")
        throw NSError(domain: "VideoTranscoder", code: 10, userInfo: [NSLocalizedDescriptionKey: "管道输出暂不支持"])
    }
}

// MARK: - Helper Functions
func printUsage() {
    print("""
    Swift-FFmpeg - 基于 Swift 的视频处理工具

    用法:
    swift-ffmpeg -i <input> [选项] -o <output>

    必需参数:
    -i <input>          输入文件或URL
    -o <output>         输出文件或 pipe:1

    时间控制:
    -ss <time>          起始时间 (秒或 HH:MM:SS.ms)
    -t <duration>       处理时长 (秒或 HH:MM:SS.ms)

    格式选项:
    -f <format>         输出格式 (mp4, mov, m4s, png, json)
    -preset <preset>    编码预设 (ultrafast, fast, medium, slow, veryslow)

    视频选项:
    -codec:v <codec>    视频编码器 (h264, hevc, av1)
    -codec:a <codec>    音频编码器 (aac, copy)
    -r <fps>            最大帧率
    -size <resolution>  输出分辨率 (480p, 720p, 1080p, 1440p, 2160p, 4320p)
    -keepHDR            保留HDR信息

    其他选项:
    -output_ts_offset <seconds>  输出时间戳偏移
    -full-keyframes             使用完整关键帧检测（慢但准确）

    示例:
    # 提取元数据
    swift-ffmpeg -i video.mp4 -f json -o metadata.json

    # 提取帧
    swift-ffmpeg -i video.mp4 -ss 10 -f png -o frame.png

    # 转码视频
    swift-ffmpeg -i input.mp4 -codec:v hevc -size 720p -o output.mp4

    # 剪切视频片段
    swift-ffmpeg -ss 30 -i input.mp4 -t 60 -o clip.mp4
    """)
}

// MARK: - Main Function
func main() async {
    do {
        guard let arguments = Arguments.parse() else {
            printUsage()
            exit(1)
        }

        // 验证必需参数
        guard arguments.inputSource != nil else {
            print("错误: 缺少输入源 (-i)")
            printUsage()
            exit(1)
        }

        if arguments.format != "json" && arguments.outputDestination == nil {
            print("错误: 缺少输出目标 (-o)")
            printUsage()
            exit(1)
        }

        let transcoder = VideoTranscoder(arguments: arguments)
        try await transcoder.process()

    } catch {
        print("错误: \(error.localizedDescription)")
        exit(1)
    }
}

// 运行主函数
let semaphore = DispatchSemaphore(value: 0)

Task {
    await main()
    semaphore.signal()
}

semaphore.wait()
