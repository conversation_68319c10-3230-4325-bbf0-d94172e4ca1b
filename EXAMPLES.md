# Swift-FFmpeg 使用示例

## 快速开始

### 1. 查看视频信息

```bash
# 提取视频元数据到控制台
./swift-ffmpeg -i video.mp4 -f json

# 保存元数据到文件
./swift-ffmpeg -i video.mp4 -f json -o metadata.json
```

输出示例：
```json
{
  "bitrate" : 4105826,
  "metadata" : {
    "software" : "Lavf60.3.100",
    "title" : "视频标题",
    "artist" : "作者"
  },
  "fps" : 25,
  "height" : 1080,
  "width" : 1920,
  "keyframes" : [0.08, 6.56, 14.28, 20.12, 24.0, ...],
  "duration" : 1830.96,
  "audio_codec" : "aac ",
  "video_codec" : "avc1"
}
```

**关键帧说明：**
- `keyframes` 数组包含真实检测的I帧时间戳（以秒为单位）
- 每个视频的关键帧分布都不同，取决于编码设置和内容
- 关键帧通常在场景变化或固定间隔出现

### 2. 提取视频帧

```bash
# 提取第10秒的帧
./swift-ffmpeg -i video.mp4 -ss 10 -f png -o frame.png

# 提取帧并缩放到720p
./swift-ffmpeg -i video.mp4 -ss 10 -f png -size 720p -o frame_720p.png

# 提取多个时间点的帧
./swift-ffmpeg -i video.mp4 -ss 30 -f png -o frame_30s.png
./swift-ffmpeg -i video.mp4 -ss 60 -f png -o frame_60s.png
```

### 3. 视频剪切

```bash
# 从第30秒开始，剪切60秒
./swift-ffmpeg -ss 30 -i video.mp4 -t 60 -o clip.mp4

# 剪切前10秒
./swift-ffmpeg -i video.mp4 -t 10 -o intro.mp4

# 剪切最后10秒（假设视频总长度为120秒）
./swift-ffmpeg -ss 110 -i video.mp4 -t 10 -o outro.mp4
```

### 4. 视频转码

```bash
# 转码为HEVC格式
./swift-ffmpeg -i input.mp4 -codec:v hevc -o output_hevc.mp4

# 使用不同的编码预设
./swift-ffmpeg -i input.mp4 -preset fast -o output_fast.mp4
./swift-ffmpeg -i input.mp4 -preset slow -o output_slow.mp4

# 转码为MOV格式
./swift-ffmpeg -i input.mp4 -f mov -o output.mov
```

### 5. 分辨率缩放

```bash
# 缩放到720p（短边为720像素）
./swift-ffmpeg -i input.mp4 -size 720p -o output_720p.mp4

# 缩放到1080p
./swift-ffmpeg -i input.mp4 -size 1080p -o output_1080p.mp4

# 缩放到4K
./swift-ffmpeg -i input.mp4 -size 2160p -o output_4k.mp4
```

### 6. 帧率控制

```bash
# 限制输出帧率为30fps
./swift-ffmpeg -i input.mp4 -r 30 -o output_30fps.mp4

# 限制帧率并缩放分辨率
./swift-ffmpeg -i input.mp4 -r 24 -size 720p -o output_24fps_720p.mp4
```

### 7. 组合操作

```bash
# 剪切 + 转码 + 缩放
./swift-ffmpeg -ss 30 -i input.mp4 -t 60 -codec:v hevc -size 720p -o final.mp4

# 剪切 + 帧率限制 + 快速预设
./swift-ffmpeg -ss 10 -i input.mp4 -t 30 -r 24 -preset fast -o quick_clip.mp4
```

### 8. 处理网络视频

```bash
# 从HTTPS URL下载并转码
./swift-ffmpeg -i https://example.com/video.mp4 -size 720p -o downloaded.mp4

# 从网络视频提取帧
./swift-ffmpeg -i https://example.com/video.mp4 -ss 10 -f png -o network_frame.png
```

## 高级用法

### 时间格式

支持两种时间格式：

1. **秒数格式**: `123.45`
2. **时分秒格式**: `HH:MM:SS.ms`

```bash
# 使用秒数格式
./swift-ffmpeg -ss 90.5 -i video.mp4 -t 30 -o clip.mp4

# 使用时分秒格式
./swift-ffmpeg -ss 00:01:30.500 -i video.mp4 -t 00:00:30 -o clip.mp4
```

### 编码预设说明

- `ultrafast`: 最快编码速度，文件较大
- `fast`: 快速编码，适合实时处理
- `medium`: 平衡速度和质量（默认）
- `slow`: 较慢编码，更好的压缩率
- `veryslow`: 最慢编码，最佳质量

### 分辨率缩放规则

分辨率缩放基于**短边**：

- 对于1920x1080的横向视频，短边是1080（高度）
- 对于1080x1920的纵向视频，短边是1080（宽度）
- 如果原始短边小于或等于目标值，保持原始分辨率

示例：
```bash
# 原始: 1920x1080 → 720p → 输出: 1280x720
# 原始: 1080x1920 → 720p → 输出: 720x1280
# 原始: 640x480 → 720p → 输出: 640x480 (不放大)
```

## 性能优化建议

1. **使用合适的预设**：
   - 快速预览: `ultrafast`
   - 日常使用: `medium`
   - 高质量归档: `slow`

2. **合理选择分辨率**：
   - 避免不必要的高分辨率处理
   - 根据最终用途选择合适的分辨率

3. **利用硬件加速**：
   - 工具自动使用Apple M系列芯片的硬件编码器
   - HEVC编码在M系列芯片上性能最佳

## 故障排除

### 常见问题

1. **文件无法播放**
   ```bash
   # 检查文件信息
   ./swift-ffmpeg -i problematic_video.mp4 -f json
   ```

2. **转码失败**
   ```bash
   # 尝试使用更兼容的设置
   ./swift-ffmpeg -i input.mp4 -preset medium -o output.mp4
   ```

3. **网络视频下载慢**
   ```bash
   # 先下载到本地再处理
   curl -o local_video.mp4 https://example.com/video.mp4
   ./swift-ffmpeg -i local_video.mp4 -size 720p -o output.mp4
   ```

### 调试技巧

```bash
# 1. 首先检查输入文件信息
./swift-ffmpeg -i input.mp4 -f json

# 2. 使用简单的转码测试
./swift-ffmpeg -i input.mp4 -o test.mp4

# 3. 逐步添加参数
./swift-ffmpeg -i input.mp4 -size 720p -o test_720p.mp4
```

## 批处理示例

### Bash脚本示例

```bash
#!/bin/bash

# 批量转码目录中的所有MP4文件为720p
for file in *.mp4; do
    if [ -f "$file" ]; then
        output="${file%.*}_720p.mp4"
        echo "处理: $file -> $output"
        ./swift-ffmpeg -i "$file" -size 720p -o "$output"
    fi
done
```

### 批量提取帧

```bash
#!/bin/bash

# 从每个视频的第10秒提取帧
for file in *.mp4; do
    if [ -f "$file" ]; then
        output="${file%.*}_frame.png"
        echo "提取帧: $file -> $output"
        ./swift-ffmpeg -i "$file" -ss 10 -f png -o "$output"
    fi
done
```
