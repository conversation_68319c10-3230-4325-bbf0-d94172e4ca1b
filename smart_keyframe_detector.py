#!/usr/bin/env python3
"""
智能关键帧检测器
使用ffmpeg分析前面10秒的真实关键帧，然后推测整个视频的关键帧分布
适用于长视频的快速关键帧检测
"""

import json
import subprocess
import sys
import os
import tempfile
import time
import re
from typing import List, Dict, Any, Tuple
from pathlib import Path

class SmartKeyframeDetector:
    def __init__(self, sample_duration: float = 10.0, max_samples: int = 1000):
        """
        初始化智能关键帧检测器

        Args:
            sample_duration: 采样时长（秒），默认10秒
            max_samples: 最大采样数量，避免处理时间过长
        """
        self.sample_duration = sample_duration
        self.max_samples = max_samples

    def get_video_info(self, video_file: str) -> Dict[str, Any]:
        """获取视频基本信息"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format', '-show_streams',
                video_file
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                raise Exception(f"ffprobe失败: {result.stderr}")

            data = json.loads(result.stdout)

            # 查找视频流
            video_stream = None
            for stream in data['streams']:
                if stream['codec_type'] == 'video':
                    video_stream = stream
                    break

            if not video_stream:
                raise Exception("未找到视频流")

            duration = float(data['format']['duration'])
            fps = eval(video_stream['r_frame_rate'])  # 例如 "25/1" -> 25.0
            width = int(video_stream['width'])
            height = int(video_stream['height'])

            return {
                'duration': duration,
                'fps': fps,
                'width': width,
                'height': height,
                'codec': video_stream['codec_name'],
                'bitrate': int(data['format'].get('bit_rate', 0))
            }

        except Exception as e:
            print(f"❌ 获取视频信息失败: {e}")
            return {}

    def extract_sample_keyframes(self, video_file: str) -> List[float]:
        """提取前N秒的真实关键帧"""
        print(f"🔍 正在分析前 {self.sample_duration} 秒的关键帧...")

        try:
            # 方法1: 使用ffprobe获取关键帧信息
            cmd = [
                'ffprobe', '-v', 'quiet', '-select_streams', 'v:0',
                '-show_entries', 'frame=best_effort_timestamp_time,key_frame',
                '-of', 'csv=p=0', '-t', str(self.sample_duration),
                video_file
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode != 0:
                print(f"⚠️  ffprobe方法失败，尝试备用方法: {result.stderr}")
                return self._extract_keyframes_alternative(video_file)

            keyframes = []
            lines = result.stdout.strip().split('\n')

            for line in lines:
                if not line:
                    continue
                parts = line.split(',')
                if len(parts) >= 2:
                    try:
                        pts_time = float(parts[0])
                        is_keyframe = int(parts[1])

                        if is_keyframe == 1 and pts_time <= self.sample_duration:
                            keyframes.append(pts_time)

                        # 限制采样数量
                        if len(keyframes) >= self.max_samples:
                            break
                    except (ValueError, IndexError):
                        continue

            keyframes.sort()
            print(f"✅ 在前 {self.sample_duration} 秒中检测到 {len(keyframes)} 个关键帧")

            # 如果没有检测到关键帧，尝试备用方法
            if not keyframes:
                print("⚠️  未检测到关键帧，尝试备用方法...")
                return self._extract_keyframes_alternative(video_file)

            return keyframes

        except Exception as e:
            print(f"❌ 关键帧提取失败: {e}")
            return self._extract_keyframes_alternative(video_file)

    def _extract_keyframes_alternative(self, video_file: str) -> List[float]:
        """备用关键帧提取方法：使用ffmpeg的scene detection"""
        try:
            print("🔄 使用场景检测方法提取关键帧...")

            # 使用ffmpeg的scene filter检测场景变化
            cmd = [
                'ffmpeg', '-i', video_file, '-t', str(self.sample_duration),
                '-vf', 'select=gt(scene\\,0.3),showinfo', '-f', 'null', '-'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            keyframes = []
            # 解析ffmpeg输出中的时间戳
            for line in result.stderr.split('\n'):
                if 'pts_time:' in line:
                    match = re.search(r'pts_time:(\d+\.?\d*)', line)
                    if match:
                        pts_time = float(match.group(1))
                        if pts_time <= self.sample_duration:
                            keyframes.append(pts_time)

            keyframes.sort()

            # 如果还是没有，生成基于GOP的估算关键帧
            if not keyframes:
                print("🎯 生成基于GOP估算的关键帧...")
                # 假设GOP大小为2秒（常见设置）
                gop_size = 2.0
                current_time = 0.0
                while current_time <= self.sample_duration:
                    keyframes.append(current_time)
                    current_time += gop_size

            print(f"✅ 备用方法检测到 {len(keyframes)} 个关键帧")
            return keyframes

        except Exception as e:
            print(f"⚠️  备用方法也失败: {e}")
            # 最后的备用方案：基于常见GOP大小生成
            keyframes = []
            gop_size = 2.0  # 2秒GOP
            current_time = 0.0
            while current_time <= self.sample_duration:
                keyframes.append(current_time)
                current_time += gop_size
            print(f"🎯 使用默认GOP模式生成 {len(keyframes)} 个关键帧")
            return keyframes

    def analyze_keyframe_pattern(self, keyframes: List[float]) -> Dict[str, float]:
        """分析关键帧模式"""
        if len(keyframes) < 2:
            return {'avg_interval': 2.0, 'min_interval': 1.0, 'max_interval': 5.0}

        intervals = []
        for i in range(1, len(keyframes)):
            interval = keyframes[i] - keyframes[i-1]
            intervals.append(interval)

        avg_interval = sum(intervals) / len(intervals)
        min_interval = min(intervals)
        max_interval = max(intervals)

        # 计算间隔的标准差，判断规律性
        variance = sum((x - avg_interval) ** 2 for x in intervals) / len(intervals)
        std_dev = variance ** 0.5

        return {
            'avg_interval': avg_interval,
            'min_interval': min_interval,
            'max_interval': max_interval,
            'std_dev': std_dev,
            'regularity': std_dev / avg_interval if avg_interval > 0 else 1.0  # 越小越规律
        }

    def predict_keyframes(self, video_duration: float, sample_keyframes: List[float],
                         pattern: Dict[str, float]) -> List[float]:
        """基于采样结果预测整个视频的关键帧"""
        print(f"🧠 正在预测整个视频的关键帧分布...")

        if not sample_keyframes:
            # 如果没有采样到关键帧，使用默认间隔
            interval = 2.0
            predicted = []
            current_time = 0.0
            while current_time < video_duration:
                predicted.append(current_time)
                current_time += interval
            return predicted

        predicted_keyframes = []
        avg_interval = pattern['avg_interval']
        regularity = pattern['regularity']

        print(f"📊 分析结果:")
        print(f"   平均间隔: {avg_interval:.2f}秒")
        print(f"   规律性指数: {regularity:.3f} (越小越规律)")

        if regularity < 0.3:  # 高度规律
            print("   📈 检测到高度规律的关键帧模式，使用固定间隔预测")
            current_time = sample_keyframes[0] if sample_keyframes else 0.0
            while current_time < video_duration:
                predicted_keyframes.append(current_time)
                current_time += avg_interval

        elif regularity < 0.8:  # 中等规律
            print("   📊 检测到中等规律的关键帧模式，使用自适应间隔预测")
            # 保留采样的关键帧
            predicted_keyframes.extend(sample_keyframes)

            # 从采样结束位置开始预测
            current_time = self.sample_duration
            while current_time < video_duration:
                # 添加一些随机性，模拟真实的关键帧分布
                variation = avg_interval * 0.2  # 20%的变化
                next_interval = avg_interval + (hash(str(current_time)) % 1000 - 500) / 1000 * variation
                next_interval = max(pattern['min_interval'], min(pattern['max_interval'], next_interval))

                current_time += next_interval
                if current_time < video_duration:
                    predicted_keyframes.append(current_time)

        else:  # 不规律
            print("   🎲 检测到不规律的关键帧模式，使用密集采样预测")
            # 对于不规律的视频，使用更密集的关键帧
            predicted_keyframes.extend(sample_keyframes)

            # 使用较小的间隔
            dense_interval = min(avg_interval * 0.7, 3.0)
            current_time = self.sample_duration
            while current_time < video_duration:
                predicted_keyframes.append(current_time)
                current_time += dense_interval

        # 确保第一帧和最后帧
        if not predicted_keyframes or predicted_keyframes[0] > 0.1:
            predicted_keyframes.insert(0, 0.0)

        if video_duration - predicted_keyframes[-1] > avg_interval:
            predicted_keyframes.append(video_duration - 0.1)

        # 排序并去重
        predicted_keyframes = sorted(list(set(round(kf, 3) for kf in predicted_keyframes)))

        print(f"✅ 预测生成 {len(predicted_keyframes)} 个关键帧")
        return predicted_keyframes

    def detect_keyframes(self, video_file: str) -> Dict[str, Any]:
        """主要的关键帧检测方法"""
        start_time = time.time()

        print(f"🎬 开始智能关键帧检测: {video_file}")

        # 1. 获取视频信息
        video_info = self.get_video_info(video_file)
        if not video_info:
            return {}

        duration = video_info['duration']
        print(f"📹 视频信息: {video_info['width']}x{video_info['height']}, "
              f"{video_info['fps']:.1f}fps, {duration:.1f}秒")

        # 2. 提取采样关键帧
        sample_keyframes = self.extract_sample_keyframes(video_file)

        # 3. 分析关键帧模式
        pattern = self.analyze_keyframe_pattern(sample_keyframes)

        # 4. 预测整个视频的关键帧
        predicted_keyframes = self.predict_keyframes(duration, sample_keyframes, pattern)

        detection_time = time.time() - start_time

        result = {
            'video_info': video_info,
            'sample_keyframes': sample_keyframes,
            'pattern_analysis': pattern,
            'predicted_keyframes': predicted_keyframes,
            'detection_time': detection_time,
            'keyframe_count': len(predicted_keyframes),
            'keyframe_density': len(predicted_keyframes) / duration if duration > 0 else 0
        }

        print(f"⏱️  检测完成，耗时 {detection_time:.3f} 秒")
        print(f"📊 关键帧密度: {result['keyframe_density']:.3f} 个/秒")

        return result

def format_time(seconds: float) -> str:
    """格式化时间显示"""
    minutes = int(seconds // 60)
    secs = seconds % 60
    return f"{minutes:02d}:{secs:06.3f}"

def save_results(result: Dict[str, Any], output_file: str = None):
    """保存检测结果"""
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"💾 结果已保存到: {output_file}")
    else:
        print("\n📋 检测结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python3 smart_keyframe_detector.py <视频文件> [输出文件] [采样时长]")
        print("示例: python3 smart_keyframe_detector.py video.mp4")
        print("示例: python3 smart_keyframe_detector.py video.mp4 result.json")
        print("示例: python3 smart_keyframe_detector.py video.mp4 result.json 15")
        return

    video_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    sample_duration = float(sys.argv[3]) if len(sys.argv) > 3 else 10.0

    if not os.path.exists(video_file):
        print(f"❌ 视频文件不存在: {video_file}")
        return

    # 创建检测器
    detector = SmartKeyframeDetector(sample_duration=sample_duration)

    # 执行检测
    result = detector.detect_keyframes(video_file)

    if result:
        # 显示简要统计
        print(f"\n📈 检测统计:")
        print(f"   总关键帧数: {result['keyframe_count']}")
        print(f"   检测时间: {result['detection_time']:.3f}秒")
        print(f"   关键帧密度: {result['keyframe_density']:.3f}个/秒")

        # 显示前10个关键帧时间点
        keyframes = result['predicted_keyframes']
        print(f"\n🎯 前10个关键帧时间点:")
        for i, kf in enumerate(keyframes[:10]):
            print(f"   {i+1:2d}: {format_time(kf)}")

        if len(keyframes) > 10:
            print(f"   ... 还有 {len(keyframes) - 10} 个关键帧")

        # 保存结果
        save_results(result, output_file)

        print(f"\n✅ 智能关键帧检测完成!")
    else:
        print(f"\n❌ 检测失败!")

if __name__ == "__main__":
    main()
