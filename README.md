# Swift-FFmpeg

基于 Swift 的高性能视频处理命令行工具，专为 Apple M 系列芯片优化，利用硬件加速实现快速的视频转码、帧提取和元数据处理。

## 特性

- 🚀 **硬件加速**: 充分利用 Apple M 系列芯片的 VideoToolbox 和 Media Engine
- 📱 **原生 Swift**: 基于 AVFoundation 框架，无需外部依赖
- ⚡ **高性能**: 优化的时间定位和转码算法
- 🎯 **简单易用**: 类似 FFmpeg 的命令行界面，但更简洁
- 🔧 **多格式支持**: 支持 MP4、MOV、fMP4、PNG 和 JSON 输出

## 系统要求

- macOS 12.0 或更高版本
- Apple M 系列芯片（M1、M2、M3、M4 等）
- Swift 5.7 或更高版本

## 编译

### 方法 1: 直接编译可执行文件

```bash
# 编译为可执行文件
swiftc -O main.swift -o swift-ffmpeg

# 移动到系统路径（可选）
sudo mv swift-ffmpeg /usr/local/bin/
```

### 方法 2: 使用 Swift 直接运行

```bash
# 直接运行 Swift 脚本
swift main.swift [参数...]
```

### 方法 3: 创建 Swift Package（推荐）

```bash
# 初始化 Swift Package
swift package init --type executable --name swift-ffmpeg

# 替换 Sources/swift-ffmpeg/main.swift 内容
# 然后编译
swift build -c release

# 可执行文件位于 .build/release/swift-ffmpeg
```

## 使用方法

### 基本语法

```bash
swift-ffmpeg -i <input> [选项] -o <output>
```

### 参数说明

#### 必需参数
- `-i <input>`: 输入文件路径或 HTTPS URL
- `-o <output>`: 输出文件路径或 `pipe:1`（标准输出）

#### 时间控制
- `-ss <time>`: 起始时间（秒或 HH:MM:SS.ms 格式）
- `-t <duration>`: 处理时长（秒或 HH:MM:SS.ms 格式）

#### 格式选项
- `-f <format>`: 输出格式
  - `mp4`: MP4 容器格式
  - `mov`: MOV 容器格式
  - `m4s`: Fragmented MP4 格式
  - `png`: 提取帧为 PNG 图片
  - `json`: 提取元数据为 JSON

#### 编码选项
- `-preset <preset>`: 编码预设
  - `ultrafast`: 最快速度，较低质量
  - `fast`: 快速编码
  - `medium`: 平衡速度和质量（默认）
  - `slow`: 较慢速度，较高质量
  - `veryslow`: 最慢速度，最高质量

#### 视频选项
- `-codec:v <codec>`: 视频编码器（h264, hevc, av1）
- `-codec:a <codec>`: 音频编码器（aac, copy）
- `-r <fps>`: 最大输出帧率
- `-size <resolution>`: 输出分辨率（基于短边）
  - `480p`, `720p`, `1080p`, `1440p`, `2160p`, `4320p`
- `-keepHDR`: 保留 HDR 信息

#### 其他选项
- `-output_ts_offset <seconds>`: 输出时间戳偏移

## 使用示例

### 1. 提取视频元数据

```bash
# 提取元数据到 JSON 文件
swift-ffmpeg -i video.mp4 -f json -o metadata.json

# 输出元数据到控制台
swift-ffmpeg -i video.mp4 -f json
```

### 2. 提取视频帧

```bash
# 提取第 10 秒的帧
swift-ffmpeg -i video.mp4 -ss 10 -f png -o frame.png

# 提取帧并缩放到 720p
swift-ffmpeg -i video.mp4 -ss 10 -f png -size 720p -o frame_720p.png
```

### 3. 视频转码

```bash
# 转码为 HEVC 格式
swift-ffmpeg -i input.mp4 -codec:v hevc -o output.mp4

# 转码并缩放到 720p
swift-ffmpeg -i input.mp4 -codec:v hevc -size 720p -o output_720p.mp4

# 使用快速预设转码
swift-ffmpeg -i input.mp4 -preset fast -o output.mp4
```

### 4. 视频剪切

```bash
# 从第 30 秒开始，剪切 60 秒
swift-ffmpeg -ss 30 -i input.mp4 -t 60 -o clip.mp4

# 剪切并转码为 HEVC
swift-ffmpeg -ss 30 -i input.mp4 -t 60 -codec:v hevc -o clip_hevc.mp4
```

### 5. 处理网络视频

```bash
# 从 HTTPS URL 下载并转码
swift-ffmpeg -i https://example.com/video.mp4 -codec:v hevc -size 720p -o output.mp4
```

### 6. 限制帧率

```bash
# 限制输出帧率为 30fps
swift-ffmpeg -i input.mp4 -r 30 -o output.mp4
```

## 性能优化

1. **硬件加速**: 工具自动使用 Apple M 系列芯片的硬件编码器
2. **预设选择**: 根据需求选择合适的预设
   - 快速预览: `ultrafast`
   - 日常使用: `medium`
   - 高质量: `slow`
3. **分辨率控制**: 使用 `-size` 参数避免不必要的高分辨率处理
4. **精确定位**: `-ss` 参数实现快速时间定位
5. **智能转码**:
   - 无参数时保持原始分辨率和质量
   - 有自定义参数时使用高级转码引擎

## 注意事项

1. **HDR 支持**: 使用 `-keepHDR` 参数保留 HDR 信息，但需要目标格式支持
2. **网络输入**: 支持 HTTPS URL，但需要稳定的网络连接
3. **管道输出**: `pipe:1` 输出功能在当前版本中有限制
4. **AV1 编码**: M4 芯片的 AV1 硬件编码支持需要 macOS 最新版本

## 故障排除

### 常见错误

1. **"输入文件不可播放"**: 检查文件格式和完整性
2. **"导出失败"**: 检查输出路径权限和磁盘空间
3. **"无法创建导出会话"**: 检查输入格式和预设兼容性

### 调试技巧

```bash
# 首先检查文件信息
swift-ffmpeg -i input.mp4 -f json

# 使用简单参数测试
swift-ffmpeg -i input.mp4 -o test.mp4
```

## 开发计划

- [ ] 完整的管道输出支持
- [ ] 更精确的关键帧检测
- [ ] 批处理模式
- [ ] 进度显示
- [ ] 更多音频处理选项
- [ ] 字幕支持

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
